## Global PTP 操作函数

### 核心命令
```bash
# 设置 ToD 时间（使用当前系统时间）
./autoeth_cli ptp_global -if enp1s0 -settod 0

# 读取 ToD 时间
./autoeth_cli ptp_global -if enp1s0 -gettod 0

# 测试 ToD 是否运行
./autoeth_cli ptp_global -if enp1s0 -testtod 0

# 读取寄存器
./autoeth_cli ptp_global -if enp1s0 -rd 0x12

# 写入寄存器
./autoeth_cli ptp_global -if enp1s0 -wr 0x12 0x3101
```

### 简化的函数列表
1. **SetToDTime()** - 设置 ToD 时间的核心函数
3. **TestToDRunning()** - 测试时钟是否运行
2. **GetToDTime()** - 读取 ToD 时间的核心函数  
4. **WaitToDReady()** - 等待 ToD 控制器就绪
5. **WaitToDComplete()** - 等待 ToD 操作完成



### 核心寄存器操作序列
```
1. 等待 ToDBusy=0 (寄存器 0x12 bit 15)
2. 写入时间值 (寄存器 0x13-0x17)
3. 设置 Load Point (寄存器 0x10-0x11)
4. 触发操作 ToDBusy=1, ToDOp=0x3 (寄存器 0x12)
5. 等待 ToDBusy=0 (操作完成)
```
