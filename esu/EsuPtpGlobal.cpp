/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"
#include <ctime>
#include <iomanip>
#include <unistd.h>  // for usleep

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1,  1 },	// Interface name
        { "-list",	0,  0 },	// List PTP Global registers
        { "-rd",	0,  1 },	// Default: read operation
        { "-wr",	0,  2 },	// write operation
		{ "-testtod",	0,  1 },	// Test if TimeArray is running
		{ "-gettod",	0,  1 },	// Get ToD time: timeArray
        { "-settod",	0,  1 },	// Set ToD time: timeArray (uses current system time)
        { "-readcfg",	0,  1 },	// Read PTP Config register
        { "-writecfg",	0,  2 },	// Write PTP Config register
        { "-v",		0,  0 },	// verbose option
} ;

// PTP Global register definitions based on Figure 17: PTP Global Register bit Map
// Global 2 offset 0x16 & 0x17 w/AVBBlock = 0x0 & AVBPort = 0x1F
static PTP_GLOBAL_Field_t     R_00[] = {
	{ 15,  0,        "PTPEtherType",		0, },
} ;

static PTP_GLOBAL_Field_t     R_01[] = {
	{ 15,  0,        "MessageTypeTSEnables",	0, },
} ;

static PTP_GLOBAL_Field_t     R_02[] = {
	{ 15,  0,        "TSArrivalCapturePointers",	0, },
} ;

static PTP_GLOBAL_Field_t     R_03[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_04[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_05[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_06[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_07[] = {
	{ 15, 15,        "U_Update",			0, },
	{ 14,  8,        "Pointer",			0, },
	{  7,  0,        "Data",			0, },
} ;

static PTP_GLOBAL_Field_t     R_08[] = {
	{ 15, 15,        "TrigGenInt",			0, },
	{ 14, 14,        "EventInt",			0, },
	{ 13, 12,        "Reserved",			0, },
	{ 11, 11,        "UpperPortsInt",		0, },
	{ 10, 10,        "PTPInt_Port10",		0, },
	{  9,  9,        "PTPInt_Port9",		0, },
	{  8,  8,        "PTPInt_Port8",		0, },
	{  7,  7,        "PTPInt_Port7",		0, },
	{  6,  6,        "PTPInt_Port6",		0, },
	{  5,  5,        "PTPInt_Port5",		0, },
	{  4,  4,        "PTPInt_Port4",		0, },
	{  3,  3,        "PTPInt_Port3",		0, },
	{  2,  2,        "PTPInt_Port2",		0, },
	{  1,  1,        "PTPInt_Port1",		0, },
	{  0,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_09[] = {
	{ 15,  1,        "Reserved",			0, },
	{  0,  0,        "PTPInt_Port11",		0, },
} ;

static PTP_GLOBAL_Field_t     R_0A[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_0B[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_0C[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_0D[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_0E[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_0F[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_10[] = {
	{ 15,  0,        "TimeOfDayLoadPort_15_0",	0, },
} ;

static PTP_GLOBAL_Field_t     R_11[] = {
	{ 15,  0,        "TimeOfDayLoadPort_31_16",	0, },
} ;

static PTP_GLOBAL_Field_t     R_12[] = {
	{ 15, 15,        "TB_TimeOfDayBusy",		0, },
	{ 14, 12,        "ToDOp",			0, },
	{ 11, 10,        "Reserved",			0, },
	{  9,  9,        "TimeArray",			0, },
	{  8,  8,        "ClkActive",			0, },
	{  7,  0,        "DomainNumber",		0, },
} ;

static PTP_GLOBAL_Field_t     R_13[] = {
	{ 15,  0,        "ToDNanoSeconds_15_0",		0, },
} ;

static PTP_GLOBAL_Field_t     R_14[] = {
	{ 15,  0,        "ToDNanoSeconds_31_16",	0, },
} ;

static PTP_GLOBAL_Field_t     R_15[] = {
	{ 15,  0,        "ToDSeconds_15_0",		0, },
} ;

static PTP_GLOBAL_Field_t     R_16[] = {
	{ 15,  0,        "ToDSeconds_31_16",		0, },
} ;

static PTP_GLOBAL_Field_t     R_17[] = {
	{ 15,  0,        "ToDSeconds_47_32",		0, },
} ;

static PTP_GLOBAL_Field_t     R_18[] = {
	{ 15,  0,        "1722NanoSeconds_15_0",	0, },
} ;

static PTP_GLOBAL_Field_t     R_19[] = {
	{ 15,  0,        "1722NanoSeconds_31_16",	0, },
} ;

static PTP_GLOBAL_Field_t     R_1A[] = {
	{ 15,  0,        "1722NanoSeconds_47_32",	0, },
} ;

static PTP_GLOBAL_Field_t     R_1B[] = {
	{ 15,  0,        "1722NanoSeconds_63_48",	0, },
} ;

static PTP_GLOBAL_Field_t     R_1C[] = {
	{ 15,  0,        "ToD1722Compensation_15_0",	0, },
} ;

static PTP_GLOBAL_Field_t     R_1D[] = {
	{ 15,  0,        "ToD1722Compensation_31_16",	0, },
} ;

static PTP_GLOBAL_Field_t     R_1E[] = {
	{ 15, 15,        "1PPSWidth",			0, },
	{ 14, 14,        "Alt",				0, },
	{ 13, 11,        "1PPSWidthRange",		0, },
	{ 10, 10,        "TCAMTimesSet",		0, },
	{  9,  9,        "1P",				0, },
	{  8,  8,        "TS",				0, },
	{  7,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_1F[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Register_t  PTP_GLOBAL_REGS[] = {
        { 0, REG_FIELD_SIZE(R_00), R_00, 0, 0 },
        { 1, REG_FIELD_SIZE(R_01), R_01, 0, 0 },
        { 2, REG_FIELD_SIZE(R_02), R_02, 0, 0 },
        { 3, REG_FIELD_SIZE(R_03), R_03, 0, 0 },
        { 4, REG_FIELD_SIZE(R_04), R_04, 0, 0 },
        { 5, REG_FIELD_SIZE(R_05), R_05, 0, 0 },
        { 6, REG_FIELD_SIZE(R_06), R_06, 0, 0 },
        { 7, REG_FIELD_SIZE(R_07), R_07, 0, 0 },
        { 8, REG_FIELD_SIZE(R_08), R_08, 0, 0 },
        { 9, REG_FIELD_SIZE(R_09), R_09, 0, 0 },
        {10, REG_FIELD_SIZE(R_0A), R_0A, 0, 0 },
        {11, REG_FIELD_SIZE(R_0B), R_0B, 0, 0 },
        {12, REG_FIELD_SIZE(R_0C), R_0C, 0, 0 },
        {13, REG_FIELD_SIZE(R_0D), R_0D, 0, 0 },
        {14, REG_FIELD_SIZE(R_0E), R_0E, 0, 0 },
        {15, REG_FIELD_SIZE(R_0F), R_0F, 0, 0 },
        {16, REG_FIELD_SIZE(R_10), R_10, 0, 0 },
        {17, REG_FIELD_SIZE(R_11), R_11, 0, 0 },
        {18, REG_FIELD_SIZE(R_12), R_12, 0, 0 },
        {19, REG_FIELD_SIZE(R_13), R_13, 0, 0 },
        {20, REG_FIELD_SIZE(R_14), R_14, 0, 0 },
        {21, REG_FIELD_SIZE(R_15), R_15, 0, 0 },
        {22, REG_FIELD_SIZE(R_16), R_16, 0, 0 },
        {23, REG_FIELD_SIZE(R_17), R_17, 0, 0 },
        {24, REG_FIELD_SIZE(R_18), R_18, 0, 0 },
        {25, REG_FIELD_SIZE(R_19), R_19, 0, 0 },
        {26, REG_FIELD_SIZE(R_1A), R_1A, 0, 0 },
        {27, REG_FIELD_SIZE(R_1B), R_1B, 0, 0 },
        {28, REG_FIELD_SIZE(R_1C), R_1C, 0, 0 },
        {29, REG_FIELD_SIZE(R_1D), R_1D, 0, 0 },
        {30, REG_FIELD_SIZE(R_1E), R_1E, 0, 0 },
        {31, REG_FIELD_SIZE(R_1F), R_1F, 0, 0 },
} ;

static REG_All_Registers_t	PTP_GLOBAL_All_Registers[] = {
	{ /* 0 */ REG_REGISTER_SIZE(PTP_GLOBAL_REGS),   PTP_GLOBAL_REGS	},	// port specific registers
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_PTP_GLOBAL  CMDT PTP Global processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_PTP_GLOBAL
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PTP Global register access
 * @details     EsuPtpGlobal::EsuPtpGlobal(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and write PTP Global registers.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuPtpGlobal::EsuPtpGlobal(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;

	// ioc_data.offs = 0x10000 ;
	ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
}

CliArgs_t * EsuPtpGlobal :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuPtpGlobal :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-readcfg", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			reg_off = StringToDecHexInt(argv[pos]) ;  // Config pointer (0x00-0x03)
			operation = 8 ;	// Read PTP Config operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-writecfg", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 2) {
			reg_off = StringToDecHexInt(argv[pos]) ;    // Config pointer (0x00-0x03)
			reg_val = StringToDecHexInt(argv[pos+1]) ;  // Data value (0x00-0xFF)
			operation = 7 ;	// Write PTP Config operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-settod", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			// -settod timeArray (uses current system time)
			operation = 6 ;	// Set ToD operation
			reg_off = StringToDecHexInt(argv[pos]) ;    // TimeArray
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			cout << "Error: Invalid number of arguments for -settod. Expected 1, got " << num << endl ;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-gettod", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			reg_off = StringToDecHexInt(argv[pos]) ;    // TimeArray
			operation = 5 ;	// Get ToD operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-testtod", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			reg_off = StringToDecHexInt(argv[pos]) ;  // TimeArray number
		}
		else {
			reg_off = 0 ;  // Default to TimeArray 0 if no parameter provided
		}
		operation = 4 ;	// Test TimeArray operation
		return (err) ;
	}


	err = ScanArgList(argc, argv, "-list", &pos, &num) ;	// List one or all sets
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 3 ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-wr", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 2) {
			reg_off = StringToDecHexInt(argv[pos]) ;
			reg_val = StringToDecHexInt(argv[pos+1]) ;
			operation = 2 ;	// Write register operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
	}
	else {

		err = ScanArgList(argc, argv, "-rd", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK) {
			if (num == 1) {
				reg_off = StringToDecHexInt(argv[pos]) ;
				operation = 1 ;	// Read register operation
			}
		}
		else {
			err = ScanArgList(argc, argv, "-A", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num > 0) {
                        	err = ReadActionArgumentList(&argv[pos], num,
                                	PTP_GLOBAL_All_Registers, sizeof(PTP_GLOBAL_All_Registers)/sizeof(PTP_GLOBAL_All_Registers_t)) ;
				operation = 4 ;	// Set register bit fields
                	}
                	else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
                        	err = IOCTL_CMD_STATUS_OK ;
                	}
		}
	}

	return (err) ;
}

int EsuPtpGlobal::ExecuteCmd(uint32_t rw, uint32_t port, uint32_t reg)
{
	uint32_t cmd ;	// G2 AVB/TSN command register: command

	// cmd := Bit 15 + rw-cmd + AvbPort=0x1F + AvbBlock=0 + AVB register
	//
	cmd = (1 << 15) | ((rw & 3) << 13) | (port << 8) | (0 << 5) | (reg & 0x1F) ;
	err = Esu::WriteIO(0x1C, 0x16, cmd) ;
	if (err == 0) {
		err = Esu::CmdDone(0x1C, 0x16, 15, 0) ;
	}
	return (err) ;
}

int EsuPtpGlobal::WriteIO(uint32_t dev, uint32_t reg, uint32_t data)
{
	// uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F

	err = Esu::WriteIO(0x1C, 0x17, data) ;	// Write data
	if (err == 0) {
		err = ExecuteCmd(3, dev, reg) ;
	}
	if (verbose) {
		cout << "WritePtpGlobal: port=0x" << hex << dev << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << data << " err:" << err << endl ;
	}
	return (err) ;
}

int EsuPtpGlobal::ReadIO(uint32_t dev, uint32_t reg, uint32_t *data)
{
	// uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F

	err = ExecuteCmd(0, dev, reg) ;
	if (err == 0) {
		err = Esu::ReadIO(0x1C, 0x17, data) ;	// Read data
	}
	if (verbose) {
		cout << "ReadPtpGlobal : port=0x" << hex << dev << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << *data << " err:" << err << endl ;
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuPtpGlobal::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuPtpGlobal::postAction(void)
{
	return (err) ;
}

int EsuPtpGlobal::DoListPtpGlobal()
{
	err = PrintRegisterList(0x1F, PTP_GLOBAL_All_Registers, sizeof(PTP_GLOBAL_All_Registers)/sizeof(REG_All_Registers_t));
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Read PTP Config register
 * @details     int EsuPtpGlobal::ReadPtpConfig(uint32_t pointer, uint32_t *data)
 * @details     Reads PTP configuration register using specified pointer
 * @param [in]  pointer: Config pointer (0x00-0x03)
 * @param [out] data: Pointer to store read data
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Read PTP Global Config register using pointer mechanism
 * @details     int EsuPtpGlobal::ReadPtpConfig(uint32_t pointer, uint32_t *data)
 * @details     Reads from PTP Global Config register at offset 0x07 using pointer mechanism
 * @param [in]  pointer: Pointer to desired config register (0x00-0x03)
 * @param [out] data: Pointer to store the read data
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::ReadPtpConfig(uint32_t pointer, uint32_t *data)
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F
	uint32_t configReg = 0 ;

	if (pointer > 0x03) {
		if (verbose) {
			cout << "Error: Invalid pointer " << pointer << ". Valid range is 0x00-0x03." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Step 1: Write pointer with Update=0 to select the config register
	configReg = (pointer << 8) ;  // Pointer bits 14:8, Update bit 15 = 0

	err = WriteIO(port, 0x07, configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error setting PTP Config pointer: " << err << endl ;
		}
		return err ;
	}

	// Step 2: Read back the register to get the data
	err = ReadIO(port, 0x07, &configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error reading PTP Config data: " << err << endl ;
		}
		return err ;
	}

	// Extract data from bits 7:0
	*data = configReg & 0xFF ;

	if (verbose) {
		cout << "Read PTP Config[0x" << hex << pointer << "] = 0x" << hex << *data << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Write PTP Global Config register using pointer mechanism
 * @details     int EsuPtpGlobal::WritePtpConfig(uint32_t pointer, uint32_t data)
 * @details     Writes to PTP Global Config register at offset 0x07 using pointer mechanism
 * @param [in]  pointer: Pointer to desired config register (0x00-0x03)
 * @param [in]  data: Data to write (8-bit value)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::WritePtpConfig(uint32_t pointer, uint32_t data)
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F
	uint32_t configReg = 0 ;

	if (pointer > 0x03) {
		if (verbose) {
			cout << "Error: Invalid pointer " << pointer << ". Valid range is 0x00-0x03." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (data > 0xFF) {
		if (verbose) {
			cout << "Error: Invalid data " << data << ". Valid range is 0x00-0xFF." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Write pointer with Update=1 and data to update the config register
	configReg = (1 << 15) | (pointer << 8) | (data & 0xFF) ;  // Update=1, Pointer, Data

	err = WriteIO(port, 0x07, configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error writing PTP Config: " << err << endl ;
		}
		return err ;
	}

	if (verbose) {
		cout << "Write PTP Config[0x" << hex << pointer << "] = 0x" << hex << data << endl ;
	}

	return 0 ;
}



int EsuPtpGlobal::Action(void)
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F
	ioc_data.error = -1 ;

	if (err == 0 && SetDevice(if_name) == true) {

		if (operation == 8)
		{		// read PTP config operation
			err = ReadPtpConfig(reg_off, &reg_val) ;
			if (err == 0) {
				cout << "PTP Config[0x" << hex << reg_off << "] = 0x" << hex << reg_val << endl ;
			}
		}
		else if (operation == 7)
		{		// write PTP config operation
			err = WritePtpConfig(reg_off, reg_val) ;
			if (err == 0) {
				cout << "PTP Config[0x" << hex << reg_off << "] = 0x" << hex << reg_val << endl ;
			}
		}
		else if (operation == 6)
		{		// set ToD operation
			// For simplicity, use current system time for now
			// In a real implementation, you'd parse the command line arguments
			time_t now = time(NULL) ;
			uint32_t seconds = (uint32_t)now ;
			uint32_t nanoseconds = 0 ;
			uint32_t domain = 1 ;  // Default domain
			err = SetToDTime(reg_off, seconds, nanoseconds, domain) ;
		}
		else if (operation == 5)
		{		// get ToD operation
			uint32_t seconds = 0 ;
			uint32_t nanoseconds = 0;
			err = GetToDTime(reg_off, &seconds, &nanoseconds) ;
		}
		else if (operation == 4)
		{		// test TimeArray operation
			err = TestToDRunning(reg_off) ;
		}
		else if (operation == 3)
		{		// list all registers
			err = DoListPtpGlobal() ;
		}
		else if (operation == 2)
		{		// write operation
			err = WriteIO(port, reg_off, reg_val) ;
			if (err == 0)
			{
				cout << "PTP_GLOBAL offset:0x" << hex << setw(8) << setfill('0')
					 << reg_off << " value:0x" << reg_val << endl  ;
			}
		}
		else if (operation == 1)
		{	// read operation
			err = ReadIO(port, reg_off, &reg_val) ;
			if (err == 0)
			{
				cout << "PTP_GLOBAL offset:0x" << hex << setw(8) << setfill('0')
						<< reg_off << " value:0x" << reg_val << endl  ;
			}
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			cout << "non sense operation" << endl ;
		}
	}
	return err ;
}




/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuPtpGlobal::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuPtpGlobal::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (	strcmp(argv[1], "PTP_GLOBAL") == 0 || strcmp(argv[1], "ptp_global") == 0 ) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/* ========================================================================================
 * 简化的 ToD 操作函数
 * ======================================================================================== */

/* *********************************************************************************//**
 * @brief       Wait for ToD Control to be ready
 * @details     int EsuPtpGlobal::WaitToDReady()
 * @details     Waits for ToDBusy bit to be clear before starting new operation
 * @return      error code: 0 on success, non-zero on timeout
 * *************************************************************************************/
int EsuPtpGlobal::WaitToDReady()
{
	uint32_t port = 0x1F ;
	uint32_t todControl = 0 ;
	uint32_t busyTimeout = 1000 ;  // 1000ms timeout

	do {
		err = ReadIO(port, 0x12, &todControl) ;
		if (err != 0) return err ;
		if ((todControl & 0x8000) == 0) break ;  // ToDBusy bit clear
		usleep(1000) ;  // 1ms delay
		busyTimeout-- ;
	} while (busyTimeout > 0) ;

	if (busyTimeout == 0) {
		if (verbose) {
			cout << "Timeout waiting for ToD Control to become ready. ToD Control: 0x" << hex << todControl << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
	}
	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Wait for ToD operation to complete
 * @details     int EsuPtpGlobal::WaitToDComplete()
 * @details     Waits for ToDBusy bit to be cleared after operation
 * @return      error code: 0 on success, non-zero on timeout
 * *************************************************************************************/
int EsuPtpGlobal::WaitToDComplete()
{
	uint32_t port = 0x1F ;
	uint32_t todControl = 0 ;
	uint32_t busyTimeout = 8000 ;  // 1000ms timeout

	do {
		err = ReadIO(port, 0x12, &todControl) ;
		if (err != 0) return err ;
		if ((todControl & 0x8000) == 0) break ;  // ToDBusy bit clear
		usleep(1000) ;  // 1ms delay
		busyTimeout-- ;
		if (busyTimeout % 100 == 0 && verbose) {
			cout << "Waiting for ToD operation to complete, remaining: " << busyTimeout << "ms, ToD Control: 0x" << hex << todControl << endl ;
		}
	} while (busyTimeout > 0) ;

	if (busyTimeout == 0) {
		if (verbose) {
			cout << "Timeout waiting for ToD operation to complete. Final ToD Control: 0x" << hex << todControl << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
	}
	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Set ToD time (simplified core function)
 * @details     int EsuPtpGlobal::SetToDTime(uint32_t timeArray, uint32_t seconds, uint32_t nanoseconds, uint32_t domain)
 * @details     Sets ToD time with proper Load Point and triggers Store All Registers operation
 * @param [in]  timeArray: Time Array to set (0 or 1)
 * @param [in]  seconds: Seconds since epoch
 * @param [in]  nanoseconds: Nanoseconds (0-999999999)
 * @param [in]  domain: IEEE 1588 domain number (0-255)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::SetToDTime(uint32_t timeArray, uint32_t seconds, uint32_t nanoseconds, uint32_t domain)
{
	uint32_t port = 0x1F ;

	if (timeArray > 1 || domain > 255) {
		if (verbose) {
			cout << "Error: Invalid parameters. TimeArray: 0-1, Domain: 0-255" << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (verbose) {
		cout << "Setting ToD Time: TimeArray=" << timeArray << ", Seconds=" << seconds << ", Nanoseconds=" << nanoseconds << ", Domain=" << domain << endl ;
	}

	// Step 1: Wait for ToD Control to be ready
	err = WaitToDReady() ;
	if (err != 0) return err ;

	// Step 2: Write ToD time values
	err = WriteIO(port, 0x13, nanoseconds & 0xFFFF) ;			// ToD NanoSeconds [15:0]
	if (err == 0) err = WriteIO(port, 0x14, (nanoseconds >> 16) & 0xFFFF) ;	// ToD NanoSeconds [31:16]
	if (err == 0) err = WriteIO(port, 0x15, seconds & 0xFFFF) ;		// ToD Seconds [15:0]
	if (err == 0) err = WriteIO(port, 0x16, (seconds >> 16) & 0xFFFF) ;	// ToD Seconds [31:16]
	if (err == 0) err = WriteIO(port, 0x17, 0) ;				// ToD Seconds [47:32] = 0

	// Step 3: Set IEEE 1722 time (same as ToD initially)
	if (err == 0) err = WriteIO(port, 0x18, nanoseconds & 0xFFFF) ;
	if (err == 0) err = WriteIO(port, 0x19, (nanoseconds >> 16) & 0xFFFF) ;
	if (err == 0) err = WriteIO(port, 0x1A, seconds & 0xFFFF) ;
	if (err == 0) err = WriteIO(port, 0x1B, (seconds >> 16) & 0xFFFF) ;

	// Step 4: Set compensation to 0
	if (err == 0) err = WriteIO(port, 0x1C, 0) ;	// Compensation [15:0]
	if (err == 0) err = WriteIO(port, 0x1D, 0) ;	// Compensation [31:16]

	// Step 5: Set Load Point (critical for operation)
	uint32_t loadPoint = seconds + 1 ;  // Load 1 second in the future
	if (err == 0) err = WriteIO(port, 0x10, loadPoint & 0xFFFF) ;		// Load Point [15:0]
	if (err == 0) err = WriteIO(port, 0x11, (loadPoint >> 16) & 0xFFFF) ;	// Load Point [31:16]

	if (err != 0) {
		if (verbose) {
			cout << "Error writing ToD time values: " << err << endl ;
		}
		return err ;
	}

	// Step 6: Trigger Store All Registers operation
	// ToDBusy=1, ToDOp=0x3 (Store All Registers), TimeArray, ClkActive=1, Domain
	uint32_t todControl = (1 << 15) | (0x3 << 12) | (timeArray << 9) | (1 << 8) | (domain & 0xFF) ;
	err = WriteIO(port, 0x12, todControl) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error triggering ToD Store operation: " << err << endl ;
		}
		return err ;
	}

	// Step 7: Wait for operation to complete
	err = WaitToDComplete() ;
	if (err != 0) return err ;

	if (verbose) {
		cout << "Successfully set ToD time for TimeArray " << timeArray << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Get ToD time (simplified)
 * @details     int EsuPtpGlobal::GetToDTime(uint32_t timeArray, uint32_t *seconds, uint32_t *nanoseconds)
 * @details     Captures and reads current ToD time from specified TimeArray
 * @param [in]  timeArray: Time Array to read (0 or 1)
 * @param [out] seconds: Pointer to store seconds value
 * @param [out] nanoseconds: Pointer to store nanoseconds value
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::GetToDTime(uint32_t timeArray, uint32_t *seconds, uint32_t *nanoseconds)
{
	uint32_t port = 0x1F ;
	uint32_t todNanoLo, todNanoHi, todSecLo, todSecMid, todSecHi ;

	if (timeArray > 1) {
		if (verbose) {
			cout << "Error: Invalid TimeArray " << timeArray << ". Valid values are 0 or 1." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Step 1: Wait for ToD Control to be ready
	err = WaitToDReady() ;
	if (err != 0) return err ;

	// Step 2: Trigger capture operation
	// ToDBusy=1, ToDOp=0x4 (Capture), TimeArray
	uint32_t todControl = (1 << 15) | (0x4 << 12) | (timeArray << 9) ;
	err = WriteIO(port, 0x12, todControl) ;
	if (err != 0) return err ;

	// Step 3: Wait for capture to complete
	err = WaitToDComplete() ;
	if (err != 0) return err ;

	// Step 4: Read captured time values
	err = ReadIO(port, 0x13, &todNanoLo) ;		// ToD NanoSeconds [15:0]
	if (err == 0) err = ReadIO(port, 0x14, &todNanoHi) ;	// ToD NanoSeconds [31:16]
	if (err == 0) err = ReadIO(port, 0x15, &todSecLo) ;	// ToD Seconds [15:0]
	if (err == 0) err = ReadIO(port, 0x16, &todSecMid) ;	// ToD Seconds [31:16]
	if (err == 0) err = ReadIO(port, 0x17, &todSecHi) ;	// ToD Seconds [47:32]

	if (err != 0) {
		if (verbose) {
			cout << "Error reading captured ToD time: " << err << endl ;
		}
		return err ;
	}

	// Step 5: Reconstruct time values
	*nanoseconds = (uint32_t(todNanoHi) << 16) | todNanoLo ;
	*seconds = (uint32_t(todSecMid) << 16) | todSecLo ;  // Ignore high 16 bits for now

	// Step 6: Convert ToD time to human readable format and compare with system time
	// 使用高精度时间戳（包含纳秒）
	uint64_t tod_timestamp_ns = (uint64_t(*seconds) * 1000000000ULL) + *nanoseconds ;

	// 获取当前系统时间（高精度）
	struct timespec current_timespec ;
	clock_gettime(CLOCK_REALTIME, &current_timespec) ;
	uint64_t current_timestamp_ns = (uint64_t(current_timespec.tv_sec) * 1000000000ULL) + current_timespec.tv_nsec ;


	if (verbose) {
		cout << "Captured ToD Time: " << *seconds << "." << setfill('0') << setw(9) << *nanoseconds << endl ;


		// 调试信息：显示原始时间戳
		cout << "Debug Info:" << endl ;
		cout << "  ToD Seconds: " << *seconds << ", Nanoseconds: " << *nanoseconds << endl ;
		cout << "  System Seconds: " << current_timespec.tv_sec << ", Nanoseconds: " << current_timespec.tv_nsec << endl ;
		cout << "  ToD Timestamp (ns): " << tod_timestamp_ns << endl ;
		cout << "  System Timestamp (ns): " << current_timestamp_ns << endl ;

		// 计算高精度时间差（包含纳秒）
		int64_t diff_ns = (int64_t)current_timestamp_ns - (int64_t)tod_timestamp_ns ;
		double diff_seconds = diff_ns / 1000000000.0 ;

		cout << "Time Difference: " << fixed << setprecision(6) << diff_seconds << " seconds" << endl ;
		cout << "                (" << dec << diff_ns << " nanoseconds)" << endl ;

		if (abs(diff_ns) < 1000000) {  // < 1ms
			cout << "Sync Status: ✓ EXCELLENT SYNC (< 1ms)" << endl ;
		} else if (abs(diff_ns) < 10000000) {  // < 10ms
			cout << "Sync Status: ✓ GOOD SYNC (< 10ms)" << endl ;
		} else if (abs(diff_ns) < 100000000) {  // < 100ms
			cout << "Sync Status: ⚠ ACCEPTABLE SYNC (< 100ms)" << endl ;
		} else if (abs(diff_seconds) < 1.0) {  // < 1s
			cout << "Sync Status: ⚠ MINOR DRIFT (< 1s)" << endl ;
		} else if (abs(diff_seconds) < 60.0) {  // < 1min
			cout << "Sync Status: ⚠ MODERATE DRIFT (< 1min)" << endl ;
		} else {
			cout << "Sync Status: ✗ OUT OF SYNC (> 1min)" << endl ;
		}
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Test if ToD is running (simplified)
 * @details     int EsuPtpGlobal::TestToDRunning(uint32_t timeArray)
 * @details     Captures time twice with delay to check if clock is incrementing
 * @param [in]  timeArray: Time Array to test (0 or 1)
 * @return      error code: 0 if running, non-zero if not running or error
 * *************************************************************************************/
int EsuPtpGlobal::TestToDRunning(uint32_t timeArray)
{
	uint32_t seconds1, nanoseconds1, seconds2, nanoseconds2 ;

	cout << "Testing if TimeArray " << timeArray << " is running..." << endl ;

	// First capture
	err = GetToDTime(timeArray, &seconds1, &nanoseconds1) ;
	if (err != 0) return err ;

	// Wait 1 second
	sleep(1) ;

	// Second capture
	err = GetToDTime(timeArray, &seconds2, &nanoseconds2) ;
	if (err != 0) return err ;

	// Compare timestamps
	uint64_t timestamp1 = (uint64_t(seconds1) * 1000000000ULL) + nanoseconds1 ;
	uint64_t timestamp2 = (uint64_t(seconds2) * 1000000000ULL) + nanoseconds2 ;

	if (timestamp2 > timestamp1) {
		uint64_t diff_ns = timestamp2 - timestamp1 ;
		cout << "✓ TimeArray " << timeArray << " IS RUNNING!" << endl ;
		cout << "  Time advanced by " << (diff_ns / 1000000) << " milliseconds" << endl ;
		return 0 ;
	} else {
		cout << "✗ TimeArray " << timeArray << " is NOT running (time did not advance)" << endl ;
		cout << "  First timestamp:  " << timestamp1 << " ns" << endl ;
		cout << "  Second timestamp: " << timestamp2 << " ns" << endl ;
		return 1 ;
	}
}

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
