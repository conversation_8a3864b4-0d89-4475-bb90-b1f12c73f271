/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"
#include <ctime>
#include <iomanip>
#include <unistd.h>  // for usleep

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1,  1 },	// Interface name
        { "-list",	0,  0 },	// List PTP Global registers
        { "-rd",	0,  1 },	// Default: read operation
        { "-wr",	0,  2 },	// write operation
        { "-A",		0,128 },	// Set PTP Global bit fields
        { "-capture",	0,  1 },	// Capture TimeArray operation
        { "-config",	0,  0 },	// Configure PTP Global basic settings
        { "-start",	0,  2 },	// Start TimeArray with domain number
        { "-stop",	0,  1 },	// Stop TimeArray
        { "-status",	0,  0 },	// Show PTP Global status
        { "-mode",	0,  3 },	// Configure PTP Mode (ptpMode relayMode altScheme)
        { "-readcfg",	0,  1 },	// Read PTP Config register
        { "-writecfg",	0,  2 },	// Write PTP Config register
        { "-test",	0,  1 },	// Test if TimeArray is running
        { "-force",	0,  2 },	// Force start TimeArray with multiple methods
        { "-v",		0,  0 },	// verbose option
} ;

// PTP Global register definitions based on Figure 17: PTP Global Register bit Map
// Global 2 offset 0x16 & 0x17 w/AVBBlock = 0x0 & AVBPort = 0x1F
static PTP_GLOBAL_Field_t     R_00[] = {
	{ 15,  0,        "PTPEtherType",		0, },
} ;

static PTP_GLOBAL_Field_t     R_01[] = {
	{ 15,  0,        "MessageTypeTSEnables",	0, },
} ;

static PTP_GLOBAL_Field_t     R_02[] = {
	{ 15,  0,        "TSArrivalCapturePointers",	0, },
} ;

static PTP_GLOBAL_Field_t     R_03[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_04[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_05[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_06[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_07[] = {
	{ 15, 15,        "U_Update",			0, },
	{ 14,  8,        "Pointer",			0, },
	{  7,  0,        "Data",			0, },
} ;

static PTP_GLOBAL_Field_t     R_08[] = {
	{ 15, 15,        "TrigGenInt",			0, },
	{ 14, 14,        "EventInt",			0, },
	{ 13, 12,        "Reserved",			0, },
	{ 11, 11,        "UpperPortsInt",		0, },
	{ 10, 10,        "PTPInt_Port10",		0, },
	{  9,  9,        "PTPInt_Port9",		0, },
	{  8,  8,        "PTPInt_Port8",		0, },
	{  7,  7,        "PTPInt_Port7",		0, },
	{  6,  6,        "PTPInt_Port6",		0, },
	{  5,  5,        "PTPInt_Port5",		0, },
	{  4,  4,        "PTPInt_Port4",		0, },
	{  3,  3,        "PTPInt_Port3",		0, },
	{  2,  2,        "PTPInt_Port2",		0, },
	{  1,  1,        "PTPInt_Port1",		0, },
	{  0,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_09[] = {
	{ 15,  1,        "Reserved",			0, },
	{  0,  0,        "PTPInt_Port11",		0, },
} ;

static PTP_GLOBAL_Field_t     R_0A[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_0B[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_0C[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_0D[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_0E[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_0F[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_10[] = {
	{ 15,  0,        "TimeOfDayLoadPort_15_0",	0, },
} ;

static PTP_GLOBAL_Field_t     R_11[] = {
	{ 15,  0,        "TimeOfDayLoadPort_31_16",	0, },
} ;

static PTP_GLOBAL_Field_t     R_12[] = {
	{ 15, 15,        "TB_TimeOfDayBusy",		0, },
	{ 14, 12,        "ToDOp",			0, },
	{ 11, 10,        "Reserved",			0, },
	{  9,  9,        "TimeArray",			0, },
	{  8,  8,        "ClkActive",			0, },
	{  7,  0,        "DomainNumber",		0, },
} ;

static PTP_GLOBAL_Field_t     R_13[] = {
	{ 15,  0,        "ToDNanoSeconds_15_0",		0, },
} ;

static PTP_GLOBAL_Field_t     R_14[] = {
	{ 15,  0,        "ToDNanoSeconds_31_16",	0, },
} ;

static PTP_GLOBAL_Field_t     R_15[] = {
	{ 15,  0,        "ToDSeconds_15_0",		0, },
} ;

static PTP_GLOBAL_Field_t     R_16[] = {
	{ 15,  0,        "ToDSeconds_31_16",		0, },
} ;

static PTP_GLOBAL_Field_t     R_17[] = {
	{ 15,  0,        "ToDSeconds_47_32",		0, },
} ;

static PTP_GLOBAL_Field_t     R_18[] = {
	{ 15,  0,        "1722NanoSeconds_15_0",	0, },
} ;

static PTP_GLOBAL_Field_t     R_19[] = {
	{ 15,  0,        "1722NanoSeconds_31_16",	0, },
} ;

static PTP_GLOBAL_Field_t     R_1A[] = {
	{ 15,  0,        "1722NanoSeconds_47_32",	0, },
} ;

static PTP_GLOBAL_Field_t     R_1B[] = {
	{ 15,  0,        "1722NanoSeconds_63_48",	0, },
} ;

static PTP_GLOBAL_Field_t     R_1C[] = {
	{ 15,  0,        "ToD1722Compensation_15_0",	0, },
} ;

static PTP_GLOBAL_Field_t     R_1D[] = {
	{ 15,  0,        "ToD1722Compensation_31_16",	0, },
} ;

static PTP_GLOBAL_Field_t     R_1E[] = {
	{ 15, 15,        "1PPSWidth",			0, },
	{ 14, 14,        "Alt",				0, },
	{ 13, 11,        "1PPSWidthRange",		0, },
	{ 10, 10,        "TCAMTimesSet",		0, },
	{  9,  9,        "1P",				0, },
	{  8,  8,        "TS",				0, },
	{  7,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Field_t     R_1F[] = {
	{ 15,  0,        "Reserved",			0, },
} ;

static PTP_GLOBAL_Register_t  PTP_GLOBAL_REGS[] = {
        { 0, REG_FIELD_SIZE(R_00), R_00, 0, 0 },
        { 1, REG_FIELD_SIZE(R_01), R_01, 0, 0 },
        { 2, REG_FIELD_SIZE(R_02), R_02, 0, 0 },
        { 3, REG_FIELD_SIZE(R_03), R_03, 0, 0 },
        { 4, REG_FIELD_SIZE(R_04), R_04, 0, 0 },
        { 5, REG_FIELD_SIZE(R_05), R_05, 0, 0 },
        { 6, REG_FIELD_SIZE(R_06), R_06, 0, 0 },
        { 7, REG_FIELD_SIZE(R_07), R_07, 0, 0 },
        { 8, REG_FIELD_SIZE(R_08), R_08, 0, 0 },
        { 9, REG_FIELD_SIZE(R_09), R_09, 0, 0 },
        {10, REG_FIELD_SIZE(R_0A), R_0A, 0, 0 },
        {11, REG_FIELD_SIZE(R_0B), R_0B, 0, 0 },
        {12, REG_FIELD_SIZE(R_0C), R_0C, 0, 0 },
        {13, REG_FIELD_SIZE(R_0D), R_0D, 0, 0 },
        {14, REG_FIELD_SIZE(R_0E), R_0E, 0, 0 },
        {15, REG_FIELD_SIZE(R_0F), R_0F, 0, 0 },
        {16, REG_FIELD_SIZE(R_10), R_10, 0, 0 },
        {17, REG_FIELD_SIZE(R_11), R_11, 0, 0 },
        {18, REG_FIELD_SIZE(R_12), R_12, 0, 0 },
        {19, REG_FIELD_SIZE(R_13), R_13, 0, 0 },
        {20, REG_FIELD_SIZE(R_14), R_14, 0, 0 },
        {21, REG_FIELD_SIZE(R_15), R_15, 0, 0 },
        {22, REG_FIELD_SIZE(R_16), R_16, 0, 0 },
        {23, REG_FIELD_SIZE(R_17), R_17, 0, 0 },
        {24, REG_FIELD_SIZE(R_18), R_18, 0, 0 },
        {25, REG_FIELD_SIZE(R_19), R_19, 0, 0 },
        {26, REG_FIELD_SIZE(R_1A), R_1A, 0, 0 },
        {27, REG_FIELD_SIZE(R_1B), R_1B, 0, 0 },
        {28, REG_FIELD_SIZE(R_1C), R_1C, 0, 0 },
        {29, REG_FIELD_SIZE(R_1D), R_1D, 0, 0 },
        {30, REG_FIELD_SIZE(R_1E), R_1E, 0, 0 },
        {31, REG_FIELD_SIZE(R_1F), R_1F, 0, 0 },
} ;

static REG_All_Registers_t	PTP_GLOBAL_All_Registers[] = {
	{ /* 0 */ REG_REGISTER_SIZE(PTP_GLOBAL_REGS),   PTP_GLOBAL_REGS	},	// port specific registers
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_PTP_GLOBAL  CMDT PTP Global processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_PTP_GLOBAL
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PTP Global register access
 * @details     EsuPtpGlobal::EsuPtpGlobal(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and write PTP Global registers.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuPtpGlobal::EsuPtpGlobal(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;

	// ioc_data.offs = 0x10000 ;
	ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
}

CliArgs_t * EsuPtpGlobal :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuPtpGlobal :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-config", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 6 ;	// Configure PTP Global
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-start", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 2) {
			reg_off = StringToDecHexInt(argv[pos]) ;    // TimeArray number
			reg_val = StringToDecHexInt(argv[pos+1]) ;  // Domain number
			operation = 7 ;	// Start TimeArray operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-force", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 2) {
			reg_off = StringToDecHexInt(argv[pos]) ;    // TimeArray number
			reg_val = StringToDecHexInt(argv[pos+1]) ;  // Domain number
			operation = 11 ;	// Force start TimeArray operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-stop", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			reg_off = StringToDecHexInt(argv[pos]) ;  // TimeArray number
			operation = 8 ;	// Stop TimeArray operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-status", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 9 ;	// Show status
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-test", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			reg_off = StringToDecHexInt(argv[pos]) ;  // TimeArray number
			operation = 10 ;	// Test TimeArray operation
		}
		else {
			reg_off = 0 ;  // Default to TimeArray 0 if no parameter provided
			operation = 10 ;	// Test TimeArray operation
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-readcfg", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			reg_off = StringToDecHexInt(argv[pos]) ;  // Config pointer (0x00-0x03)
			operation = 12 ;	// Read PTP Config operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-writecfg", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 2) {
			reg_off = StringToDecHexInt(argv[pos]) ;    // Config pointer (0x00-0x03)
			reg_val = StringToDecHexInt(argv[pos+1]) ;  // Data value (0x00-0xFF)
			operation = 13 ;	// Write PTP Config operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-list", &pos, &num) ;	// List one or all sets
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 3 ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-capture", &pos, &num) ;	// Capture TimeArray
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			reg_off = StringToDecHexInt(argv[pos]) ;  // TimeArray number (0 or 1)
			operation = 5 ;	// Capture TimeArray operation
		}
		else {
			reg_off = 0 ;  // Default to TimeArray 0 if no parameter provided
			operation = 5 ;	// Capture TimeArray operation
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-wr", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 2) {
			reg_off = StringToDecHexInt(argv[pos]) ;
			reg_val = StringToDecHexInt(argv[pos+1]) ;
			operation = 2 ;	// Write register operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
	}
	else {

		err = ScanArgList(argc, argv, "-rd", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK) {
			if (num == 1) {
				reg_off = StringToDecHexInt(argv[pos]) ;
				operation = 1 ;	// Read register operation
			}
		}
		else {
			err = ScanArgList(argc, argv, "-A", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num > 0) {
                        	err = ReadActionArgumentList(&argv[pos], num,
                                	PTP_GLOBAL_All_Registers, sizeof(PTP_GLOBAL_All_Registers)/sizeof(PTP_GLOBAL_All_Registers_t)) ;
				operation = 4 ;	// Set register bit fields
                	}
                	else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
                        	err = IOCTL_CMD_STATUS_OK ;
                	}
		}
	}

	return (err) ;
}

int EsuPtpGlobal::ExecuteCmd(uint32_t rw, uint32_t port, uint32_t reg)
{
	uint32_t cmd ;	// G2 AVB/TSN command register: command

	// cmd := Bit 15 + rw-cmd + AvbPort=0x1F + AvbBlock=0 + AVB register
	//
	cmd = (1 << 15) | ((rw & 3) << 13) | (port << 8) | (0 << 5) | (reg & 0x1F) ;
	err = Esu::WriteIO(0x1C, 0x16, cmd) ;
	if (err == 0) {
		err = Esu::CmdDone(0x1C, 0x16, 15, 0) ;
	}
	return (err) ;
}

int EsuPtpGlobal::WriteIO(uint32_t dev, uint32_t reg, uint32_t data)
{
	// uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F

	err = Esu::WriteIO(0x1C, 0x17, data) ;	// Write data
	if (err == 0) {
		err = ExecuteCmd(3, dev, reg) ;
	}
	if (verbose) {
		cout << "WritePtpGlobal: port=0x" << hex << dev << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << data << " err:" << err << endl ;
	}
	return (err) ;
}

int EsuPtpGlobal::ReadIO(uint32_t dev, uint32_t reg, uint32_t *data)
{
	// uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F

	err = ExecuteCmd(0, dev, reg) ;
	if (err == 0) {
		err = Esu::ReadIO(0x1C, 0x17, data) ;	// Read data
	}
	if (verbose) {
		cout << "ReadPtpGlobal : port=0x" << hex << dev << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << *data << " err:" << err << endl ;
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuPtpGlobal::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuPtpGlobal::postAction(void)
{
	return (err) ;
}

int EsuPtpGlobal::DoListPtpGlobal()
{
	err = PrintRegisterList(0x1F, PTP_GLOBAL_All_Registers, sizeof(PTP_GLOBAL_All_Registers)/sizeof(REG_All_Registers_t));
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Capture Time Array using ToD Control register
 * @details     int EsuPtpGlobal::CaptureTimeArray(uint32_t timeArray)
 * @details     Performs a capture operation on the specified Time Array using ToDOp = 0x4.
 *              This operation captures the selected TimeArray, sets Comp=Comp & ToDLoadPt=PTPGT.
 *              The captured values can then be read from the ToD registers.
 * @param [in]  timeArray: Time Array to capture (0 or 1)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::CaptureTimeArray(uint32_t timeArray)
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F
	uint32_t todControl = 0 ;
	uint32_t busyTimeout = 1000 ;		// Timeout counter for busy wait

	if (timeArray > 1) {
		if (verbose) {
			cout << "Error: Invalid TimeArray " << timeArray << ". Valid values are 0 or 1." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Step 1: Check if ToD Control is busy
	do {
		err = ReadIO(port, 0x12, &todControl) ;
		if (err != 0) {
			if (verbose) {
				cout << "Error reading ToD Control register: " << err << endl ;
			}
			return err ;
		}

		if ((todControl & 0x8000) == 0) {  // TB bit (bit 15) is clear, not busy
			break ;
		}

		busyTimeout-- ;
		if (busyTimeout == 0) {
			if (verbose) {
				cout << "Timeout waiting for ToD Control to become ready" << endl ;
			}
			return IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
		}
	} while (busyTimeout > 0) ;

	// Step 2: Set up ToD Control register for capture operation
	// ToDBusy=1, ToDOp=0x4 (bits 14:12), TimeArray=timeArray (bit 9)
	todControl = (1 << 15) | (0x4 << 12) | (timeArray << 9) ;

	err = WriteIO(port, 0x12, todControl) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error writing ToD Control register: " << err << endl ;
		}
		return err ;
	}

	// Step 3: Wait for operation to complete (TB bit to clear)
	busyTimeout = 1000 ;
	do {
		err = ReadIO(port, 0x12, &todControl) ;
		if (err != 0) {
			if (verbose) {
				cout << "Error reading ToD Control register during capture: " << err << endl ;
			}
			return err ;
		}

		if ((todControl & 0x8000) == 0) {  // TB bit (bit 15) is clear, operation complete
			break ;
		}

		busyTimeout-- ;
		if (busyTimeout == 0) {
			if (verbose) {
				cout << "Timeout waiting for capture operation to complete" << endl ;
			}
			return IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
		}
	} while (busyTimeout > 0) ;

	if (verbose) {
		cout << "Successfully captured TimeArray " << timeArray << endl ;
	}

	// Step 4: Read and display the captured time values
	uint32_t todNanoLo, todNanoHi, todSecLo, todSecMid, todSecHi ;
	uint32_t ieee1722NanoLo, ieee1722NanoHi, ieee1722NanoMid, ieee1722NanoHiHi ;
	uint32_t compLo, compHi ;
	uint32_t domainNum ;

	// Read ToD time (10 bytes: 0x13-0x17)
	err = ReadIO(port, 0x13, &todNanoLo) ;		// ToD NanoSeconds [15:0]
	if (err == 0) err = ReadIO(port, 0x14, &todNanoHi) ;	// ToD NanoSeconds [31:16]
	if (err == 0) err = ReadIO(port, 0x15, &todSecLo) ;	// ToD Seconds [15:0]
	if (err == 0) err = ReadIO(port, 0x16, &todSecMid) ;	// ToD Seconds [31:16]
	if (err == 0) err = ReadIO(port, 0x17, &todSecHi) ;	// ToD Seconds [47:32]

	// Read IEEE 1722 time (8 bytes: 0x18-0x1B)
	if (err == 0) err = ReadIO(port, 0x18, &ieee1722NanoLo) ;	// 1722 NanoSeconds [15:0]
	if (err == 0) err = ReadIO(port, 0x19, &ieee1722NanoHi) ;	// 1722 NanoSeconds [31:16]
	if (err == 0) err = ReadIO(port, 0x1A, &ieee1722NanoMid) ;	// 1722 NanoSeconds [47:32]
	if (err == 0) err = ReadIO(port, 0x1B, &ieee1722NanoHiHi) ;	// 1722 NanoSeconds [63:48]

	// Read Compensation (4 bytes: 0x1C-0x1D)
	if (err == 0) err = ReadIO(port, 0x1C, &compLo) ;		// Compensation [15:0]
	if (err == 0) err = ReadIO(port, 0x1D, &compHi) ;		// Compensation [31:16]

	// Read Domain Number from ToD Control register
	if (err == 0) err = ReadIO(port, 0x12, &todControl) ;
	domainNum = todControl & 0xFF ;  // bits 7:0

	if (err != 0) {
		if (verbose) {
			cout << "Error reading captured time values: " << err << endl ;
		}
		return err ;
	}

	// Display the captured time values
	cout << "=== Captured TimeArray " << timeArray << " Values ===" << endl ;

	// Reconstruct and display ToD time
	uint64_t todNanoSeconds = (uint64_t(todNanoHi) << 16) | todNanoLo ;
	uint64_t todSeconds = (uint64_t(todSecHi) << 32) | (uint64_t(todSecMid) << 16) | todSecLo ;

	cout << "ToD Time:" << endl ;
	cout << "  Seconds: " << dec << todSeconds << " (0x" << hex << todSeconds << ")" << endl ;
	cout << "  NanoSeconds: " << dec << todNanoSeconds << " (0x" << hex << todNanoSeconds << ")" << endl ;

	// Convert to human readable time
	time_t timestamp = (time_t)todSeconds ;
	struct tm *utc_tm = gmtime(&timestamp) ;
	struct tm *local_tm = localtime(&timestamp) ;

	if (utc_tm != NULL && local_tm != NULL) {
		char utc_str[64] ;
		char local_str[64] ;

		strftime(utc_str, sizeof(utc_str), "%Y-%m-%d %H:%M:%S", utc_tm) ;
		strftime(local_str, sizeof(local_str), "%Y-%m-%d %H:%M:%S", local_tm) ;

		cout << "  UTC Time: " << utc_str ;
		if (todNanoSeconds > 0) {
			cout << "." << setfill('0') << setw(9) << todNanoSeconds ;
		}
		cout << " UTC" << endl ;

		cout << "  Local Time: " << local_str ;
		if (todNanoSeconds > 0) {
			cout << "." << setfill('0') << setw(9) << todNanoSeconds ;
		}
		cout << endl ;

		// Calculate time since epoch
		time_t now = time(NULL) ;
		double diff = difftime(now, timestamp) ;
		if (diff > 0) {
			cout << "  Time Difference: " << fixed << setprecision(1) << diff << " seconds ago" << endl ;
		} else if (diff < 0) {
			cout << "  Time Difference: " << fixed << setprecision(1) << -diff << " seconds in the future" << endl ;
		} else {
			cout << "  Time Difference: Current time" << endl ;
		}
	} else {
		cout << "  (Unable to convert to human readable time)" << endl ;
	}

	// Reconstruct and display IEEE 1722 time
	uint64_t ieee1722NanoSeconds = (uint64_t(ieee1722NanoHiHi) << 48) |
	                               (uint64_t(ieee1722NanoMid) << 32) |
	                               (uint64_t(ieee1722NanoHi) << 16) |
	                               ieee1722NanoLo ;

	cout << "IEEE 1722 Time:" << endl ;
	cout << "  NanoSeconds: " << dec << ieee1722NanoSeconds << " (0x" << hex << ieee1722NanoSeconds << ")" << endl ;

	// Display compensation
	uint32_t compensation = (uint32_t(compHi) << 16) | compLo ;
	cout << "Compensation: " << dec << compensation << " (0x" << hex << compensation << ")" << endl ;

	// Display domain number and clock status
	cout << "Domain Number: " << dec << domainNum << endl ;
	cout << "Clock Active: " << ((todControl & 0x100) ? "Yes" : "No") << " (bit 8)" << endl ;
	cout << "Time Array: " << ((todControl & 0x200) ? "1" : "0") << " (bit 9)" << endl ;

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Configure PTP Global basic settings
 * @details     int EsuPtpGlobal::ConfigurePtpGlobal()
 * @details     Configures basic PTP Global settings including EtherType and message type enables
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::ConfigurePtpGlobal()
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F

	if (verbose) {
		cout << "Configuring PTP Global basic settings..." << endl ;
	}

	// Set PTP EtherType (0x88F7 for IEEE 1588)
	err = WriteIO(port, 0x00, 0x88F7) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error setting PTP EtherType: " << err << endl ;
		}
		return err ;
	}

	// Enable common PTP message types for timestamping
	// Bit 0: Sync, Bit 1: Delay_Req, Bit 2: Pdelay_Req, Bit 3: Pdelay_Resp
	// Bit 8: Follow_Up, Bit 9: Delay_Resp, Bit 11: Announce
	uint32_t msgTypeEn = 0x0B0F ;  // Enable Sync, Delay_Req, Pdelay_Req, Pdelay_Resp, Follow_Up, Delay_Resp, Announce
	err = WriteIO(port, 0x01, msgTypeEn) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error setting Message Type Enables: " << err << endl ;
		}
		return err ;
	}

	// Configure Time Stamp Arrival Capture Pointers
	// Use PTPArr0Time for most message types (0x0000)
	err = WriteIO(port, 0x02, 0x0000) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error setting TS Arrival Capture Pointers: " << err << endl ;
		}
		return err ;
	}

	if (verbose) {
		cout << "PTP Global basic configuration completed successfully" << endl ;
		cout << "  EtherType: 0x88F7 (IEEE 1588)" << endl ;
		cout << "  Message Type Enables: 0x" << hex << msgTypeEn << endl ;
		cout << "  TS Arrival Pointers: 0x0000 (use PTPArr0Time)" << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Start a Time Array with specified domain
 * @details     int EsuPtpGlobal::StartTimeArray(uint32_t timeArray, uint32_t domainNum)
 * @details     Initializes and starts a Time Array with current time and domain number
 * @param [in]  timeArray: Time Array to start (0 or 1)
 * @param [in]  domainNum: IEEE 1588 domain number (0-255)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::StartTimeArray(uint32_t timeArray, uint32_t domainNum)
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F
	uint32_t todControl = 0 ;
	uint32_t busyTimeout = 1000 ;

	if (timeArray > 1) {
		if (verbose) {
			cout << "Error: Invalid TimeArray " << timeArray << ". Valid values are 0 or 1." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (domainNum > 255) {
		if (verbose) {
			cout << "Error: Invalid Domain Number " << domainNum << ". Valid range is 0-255." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (verbose) {
		cout << "Starting TimeArray " << timeArray << " with Domain " << domainNum << "..." << endl ;
	}

	// Step 1: Wait for ToD Control to be ready
	do {
		err = ReadIO(port, 0x12, &todControl) ;
		if (err != 0) return err ;
		if ((todControl & 0x8000) == 0) break ;  // TB bit clear
		usleep(1000) ;  // 延时1毫秒
		busyTimeout-- ;
		if (busyTimeout % 100 == 0 && verbose) {
			cout << "Waiting for ToD Control to become ready, remaining: " << busyTimeout << "ms, ToD Control: 0x" << hex << todControl << endl ;
		}
	} while (busyTimeout > 0) ;

	if (busyTimeout == 0) {
		if (verbose) {
			cout << "Timeout waiting for ToD Control to become ready. ToD Control: 0x" << hex << todControl << endl ;
			cout << "Attempting to force clear ToDBusy bit..." << endl ;
			// 尝试强制清除 ToDBusy 位
			err = WriteIO(port, 0x12, 0x0000) ;
			if (err == 0) {
				cout << "Force cleared ToD Control register" << endl ;
			}
		}
		// 继续执行，不返回错误
	}

	// Step 2: Set initial ToD time to current system time
	time_t now = time(NULL) ;
	uint32_t currentTime = (uint32_t)now ;

	if (verbose) {
		struct tm *utc_tm = gmtime(&now) ;
		char time_str[64] ;
		if (utc_tm != NULL) {
			strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", utc_tm) ;
			cout << "Setting initial time to current system time: " << time_str << " UTC" << endl ;
		}
	}

	err = WriteIO(port, 0x13, 0) ;			// ToD NanoSeconds [15:0] = 0
	if (err == 0) err = WriteIO(port, 0x14, 0) ;	// ToD NanoSeconds [31:16] = 0
	if (err == 0) err = WriteIO(port, 0x15, currentTime & 0xFFFF) ;		// ToD Seconds [15:0]
	if (err == 0) err = WriteIO(port, 0x16, (currentTime >> 16) & 0xFFFF) ;	// ToD Seconds [31:16]
	if (err == 0) err = WriteIO(port, 0x17, 0) ;	// ToD Seconds [47:32] = 0

	// Step 3: Set IEEE 1722 time (can be same as ToD time initially)
	if (err == 0) err = WriteIO(port, 0x18, 0) ;	// 1722 NanoSeconds [15:0]
	if (err == 0) err = WriteIO(port, 0x19, 0) ;	// 1722 NanoSeconds [31:16]
	if (err == 0) err = WriteIO(port, 0x1A, currentTime & 0xFFFF) ;		// 1722 NanoSeconds [47:32]
	if (err == 0) err = WriteIO(port, 0x1B, (currentTime >> 16) & 0xFFFF) ;	// 1722 NanoSeconds [63:48]

	// Step 4: Set compensation to 0 initially
	if (err == 0) err = WriteIO(port, 0x1C, 0) ;	// Compensation [15:0]
	if (err == 0) err = WriteIO(port, 0x1D, 0) ;	// Compensation [31:16]

	// Step 5: Set Load Point (CRITICAL: Required for ToD Store operation)
	// Load Point should be set to current TAI Global Time + small offset
	// We'll set it to current time + 1000 TSClkPer (about 1ms) to ensure it's in the future
	uint32_t loadPoint = currentTime + 1 ;  // Add 1 second for safety
	if (err == 0) err = WriteIO(port, 0x10, loadPoint & 0xFFFF) ;	// ToD Load Point [15:0]
	if (err == 0) err = WriteIO(port, 0x11, (loadPoint >> 16) & 0xFFFF) ;	// ToD Load Point [31:16]

	if (err != 0) {
		if (verbose) {
			cout << "Error setting time values and load point: " << err << endl ;
		}
		return err ;
	}

	// Step 6: Configure ToD Control and start the TimeArray
	// ToDBusy=1, ToDOp=0x3 (Store All Registers), TimeArray=timeArray, ClkActive=1, DomainNumber=domainNum
	todControl = (1 << 15) | (0x3 << 12) | (timeArray << 9) | (1 << 8) | (domainNum & 0xFF) ;

	err = WriteIO(port, 0x12, todControl) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error starting TimeArray: " << err << endl ;
		}
		return err ;
	}

	// Step 6: Wait for operation to complete
	busyTimeout = 2000 ;  // 1000次，每次1ms = 最多1秒
	do {
		err = ReadIO(port, 0x12, &todControl) ;
		if (err != 0) return err ;
		if ((todControl & 0x8000) == 0) break ;  // TB bit clear
		usleep(1000) ;  // 延时1毫秒
		busyTimeout-- ;
		if (busyTimeout % 100 == 0 && verbose) {
			cout << "Waiting for ToD operation to complete, remaining: " << busyTimeout << "ms, ToD Control: 0x" << hex << todControl << endl ;
		}
	} while (busyTimeout > 0) ;

	if (busyTimeout == 0) {
		if (verbose) {
			cout << "Timeout waiting for start operation to complete. Final ToD Control: 0x" << hex << todControl << endl ;
			cout << "ToDBusy bit is still set, operation may have failed or hardware issue." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
	}

	if (verbose) {
		cout << "Successfully started TimeArray " << timeArray << endl ;
		cout << "  Domain Number: " << domainNum << endl ;
		cout << "  Initial Time: " << currentTime << " seconds since epoch" << endl ;
		cout << "  Clock Active: Yes" << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Stop a Time Array
 * @details     int EsuPtpGlobal::StopTimeArray(uint32_t timeArray)
 * @details     Stops the specified Time Array by clearing the ClkActive bit
 * @param [in]  timeArray: Time Array to stop (0 or 1)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::StopTimeArray(uint32_t timeArray)
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F
	uint32_t todControl = 0 ;
	uint32_t busyTimeout = 1000 ;

	if (timeArray > 1) {
		if (verbose) {
			cout << "Error: Invalid TimeArray " << timeArray << ". Valid values are 0 or 1." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (verbose) {
		cout << "Stopping TimeArray " << timeArray << "..." << endl ;
	}

	// Step 1: Wait for ToD Control to be ready
	do {
		err = ReadIO(port, 0x12, &todControl) ;
		if (err != 0) return err ;
		if ((todControl & 0x8000) == 0) break ;  // TB bit clear
		busyTimeout-- ;
	} while (busyTimeout > 0) ;

	if (busyTimeout == 0) {
		if (verbose) {
			cout << "Timeout waiting for ToD Control to become ready" << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
	}

	// Step 2: Configure ToD Control to stop the TimeArray
	// ToDBusy=1, ToDOp=0x3 (Store All Registers), TimeArray=timeArray, ClkActive=0, keep current domain
	uint32_t currentDomain = todControl & 0xFF ;  // Preserve current domain
	todControl = (1 << 15) | (0x3 << 12) | (timeArray << 9) | (0 << 8) | currentDomain ;  // ToDBusy=1, ClkActive=0

	err = WriteIO(port, 0x12, todControl) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error stopping TimeArray: " << err << endl ;
		}
		return err ;
	}

	// Step 3: Wait for operation to complete
	busyTimeout = 1000 ;
	do {
		err = ReadIO(port, 0x12, &todControl) ;
		if (err != 0) return err ;
		if ((todControl & 0x8000) == 0) break ;  // TB bit clear
		busyTimeout-- ;
	} while (busyTimeout > 0) ;

	if (busyTimeout == 0) {
		if (verbose) {
			cout << "Timeout waiting for stop operation to complete" << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
	}

	if (verbose) {
		cout << "Successfully stopped TimeArray " << timeArray << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Show PTP Global status
 * @details     int EsuPtpGlobal::ShowPtpGlobalStatus()
 * @details     Displays comprehensive PTP Global status information
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::ShowPtpGlobalStatus()
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F
	uint32_t regVal = 0 ;
	uint32_t ptpModeData = 0 ;

	cout << "=== PTP Global Status ===" << endl ;

	// Read PTP EtherType
	err = ReadIO(port, 0x00, &regVal) ;
	if (err == 0) {
		cout << "PTP EtherType: 0x" << hex << regVal << endl ;
	}

	// Read Message Type Enables
	if (err == 0) err = ReadIO(port, 0x01, &regVal) ;
	if (err == 0) {
		cout << "Message Type Enables: 0x" << hex << regVal << endl ;
	}

	// Read TS Arrival Pointers
	if (err == 0) err = ReadIO(port, 0x02, &regVal) ;
	if (err == 0) {
		cout << "TS Arrival Pointers: 0x" << hex << regVal << endl ;
	}

	// Read PTP Status (offset 0x08)
	if (err == 0) err = ReadIO(port, 0x08, &regVal) ;
	if (err == 0) {
		cout << "PTP Status (0x08): 0x" << hex << regVal << endl ;
		if (regVal & 0x8000) cout << "  - TrigGen Interrupt: Active (ROC)" << endl ;
		if (regVal & 0x4000) cout << "  - Event Interrupt: Active" << endl ;
		if (regVal & 0x0800) cout << "  - Upper Ports Interrupt: Active" << endl ;

		// Check individual port interrupts (bits 10:1)
		bool anyPortInt = false ;
		for (int i = 1; i <= 10; i++) {
			if (regVal & (1 << i)) {
				cout << "  - Port " << i << " PTP Interrupt: Active" << endl ;
				anyPortInt = true ;
			}
		}
		if (!anyPortInt && !(regVal & 0xFFE)) {
			cout << "  - No port interrupts active" << endl ;
		}
	}

	// Read PTP Status Extended (offset 0x09) for Port 11
	uint32_t regVal09 = 0 ;
	if (err == 0) err = ReadIO(port, 0x09, &regVal09) ;
	if (err == 0) {
		cout << "PTP Status Extended (0x09): 0x" << hex << regVal09 << endl ;
		if (regVal09 & 0x0001) {
			cout << "  - Port 11 PTP Interrupt: Active" << endl ;
		} else {
			cout << "  - Port 11 PTP Interrupt: Inactive" << endl ;
		}
	}

	// Read ToD Control
	if (err == 0) err = ReadIO(port, 0x12, &regVal) ;
	if (err == 0) {
		cout << "ToD Control: 0x" << hex << regVal << endl ;
		cout << "  - ToD Busy: " << ((regVal & 0x8000) ? "Yes" : "No") << endl ;
		cout << "  - ToD Opcode: 0x" << hex << ((regVal >> 12) & 0x7) << endl ;
		cout << "  - Time Array: " << ((regVal & 0x200) ? "1" : "0") << endl ;
		cout << "  - Clock Active: " << ((regVal & 0x100) ? "Yes" : "No") << endl ;
		cout << "  - Domain Number: " << dec << (regVal & 0xFF) << endl ;
	}

	// Read PTP Mode configuration
	if (err == 0) err = ReadPtpConfig(0x00, &ptpModeData) ;
	if (err == 0) {
		cout << "PTP Mode Configuration: 0x" << hex << ptpModeData << endl ;
		uint32_t ptpMode = ptpModeData & 0x3 ;
		uint32_t relayMode = (ptpModeData >> 3) & 0x1 ;
		uint32_t altScheme = (ptpModeData >> 4) & 0x1 ;

		cout << "  - PTP Mode: " << ptpMode ;
		switch (ptpMode) {
			case 0: cout << " (Boundary Clock)" ; break ;
			case 1: cout << " (Peer to Peer Transparent Clock)" ; break ;
			case 2: cout << " (End to End Transparent Clock)" ; break ;
			case 3: cout << " (Reserved)" ; break ;
		}
		cout << endl ;
		cout << "  - Relay Mode: " << relayMode << ((relayMode == 0) ? " (End-station)" : " (Relay)") << endl ;
		cout << "  - Alt Scheme: " << altScheme << ((altScheme == 0) ? " (Normal)" : " (Alternative)") << endl ;
	}

	if (err != 0) {
		if (verbose) {
			cout << "Error reading PTP Global status: " << err << endl ;
		}
	}

	return err ;
}

/* *********************************************************************************//**
 * @brief       Read PTP Global Config register using pointer mechanism
 * @details     int EsuPtpGlobal::ReadPtpConfig(uint32_t pointer, uint32_t *data)
 * @details     Reads from PTP Global Config register at offset 0x07 using pointer mechanism
 * @param [in]  pointer: Pointer to desired config register (0x00-0x03)
 * @param [out] data: Pointer to store the read data
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::ReadPtpConfig(uint32_t pointer, uint32_t *data)
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F
	uint32_t configReg = 0 ;

	if (pointer > 0x03) {
		if (verbose) {
			cout << "Error: Invalid pointer " << pointer << ". Valid range is 0x00-0x03." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Step 1: Write pointer with Update=0 to select the config register
	configReg = (pointer << 8) ;  // Pointer bits 14:8, Update bit 15 = 0

	err = WriteIO(port, 0x07, configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error setting PTP Config pointer: " << err << endl ;
		}
		return err ;
	}

	// Step 2: Read back the register to get the data
	err = ReadIO(port, 0x07, &configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error reading PTP Config data: " << err << endl ;
		}
		return err ;
	}

	// Extract data from bits 7:0
	*data = configReg & 0xFF ;

	if (verbose) {
		cout << "Read PTP Config[0x" << hex << pointer << "] = 0x" << hex << *data << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Write PTP Global Config register using pointer mechanism
 * @details     int EsuPtpGlobal::WritePtpConfig(uint32_t pointer, uint32_t data)
 * @details     Writes to PTP Global Config register at offset 0x07 using pointer mechanism
 * @param [in]  pointer: Pointer to desired config register (0x00-0x03)
 * @param [in]  data: Data to write (8-bit value)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::WritePtpConfig(uint32_t pointer, uint32_t data)
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F
	uint32_t configReg = 0 ;

	if (pointer > 0x03) {
		if (verbose) {
			cout << "Error: Invalid pointer " << pointer << ". Valid range is 0x00-0x03." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (data > 0xFF) {
		if (verbose) {
			cout << "Error: Invalid data " << data << ". Valid range is 0x00-0xFF." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Write pointer with Update=1 and data to update the config register
	configReg = (1 << 15) | (pointer << 8) | (data & 0xFF) ;  // Update=1, Pointer, Data

	err = WriteIO(port, 0x07, configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error writing PTP Config: " << err << endl ;
		}
		return err ;
	}

	if (verbose) {
		cout << "Write PTP Config[0x" << hex << pointer << "] = 0x" << hex << data << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Configure PTP Mode settings
 * @details     int EsuPtpGlobal::ConfigurePtpMode(uint32_t ptpMode, uint32_t relayMode, uint32_t altScheme)
 * @details     Configures PTP Mode register (Index 0x00) with specified settings
 * @param [in]  ptpMode: PTP Mode (0=Boundary Clock, 1=P2P Transparent, 2=E2E Transparent)
 * @param [in]  relayMode: Relay Mode (0=End-station, 1=Relay for 802.1AS)
 * @param [in]  altScheme: Alternate Scheme (0=Normal, 1=Alternative for Time Domain 0)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpGlobal::ConfigurePtpMode(uint32_t ptpMode, uint32_t relayMode, uint32_t altScheme)
{
	uint32_t modeData = 0 ;

	// Validate parameters
	if (ptpMode > 3) {
		if (verbose) {
			cout << "Error: Invalid PTP Mode " << ptpMode << ". Valid range is 0-3." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (relayMode > 1) {
		if (verbose) {
			cout << "Error: Invalid Relay Mode " << relayMode << ". Valid range is 0-1." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (altScheme > 1) {
		if (verbose) {
			cout << "Error: Invalid Alt Scheme " << altScheme << ". Valid range is 0-1." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Build the mode data byte
	// Bits 7:5 = Reserved (0)
	// Bit 4 = AltScheme
	// Bit 3 = RelayMode
	// Bit 2 = Reserved (0)
	// Bits 1:0 = PTPMode
	modeData = (altScheme << 4) | (relayMode << 3) | (ptpMode & 0x3) ;

	// Write to PTP Mode register (Index 0x00)
	err = WritePtpConfig(0x00, modeData) ;
	if (err != 0) {
		return err ;
	}

	if (verbose) {
		cout << "PTP Mode Configuration:" << endl ;
		cout << "  PTP Mode: " << ptpMode ;
		switch (ptpMode) {
			case 0: cout << " (Boundary Clock)" ; break ;
			case 1: cout << " (Peer to Peer Transparent Clock)" ; break ;
			case 2: cout << " (End to End Transparent Clock)" ; break ;
			case 3: cout << " (Reserved)" ; break ;
		}
		cout << endl ;
		cout << "  Relay Mode: " << relayMode << ((relayMode == 0) ? " (End-station)" : " (Relay)") << endl ;
		cout << "  Alt Scheme: " << altScheme << ((altScheme == 0) ? " (Normal)" : " (Alternative)") << endl ;
		cout << "  Mode Data: 0x" << hex << modeData << endl ;
	}

	return 0 ;
}

int EsuPtpGlobal::WriteAllRegisterList(uint32_t port)
{
	Esu::SetAllRegisterData(PTP_GLOBAL_All_Registers,sizeof(PTP_GLOBAL_All_Registers)/sizeof(REG_All_Registers_t)) ;

	for (uint32_t idx = 0; err == 0 && (idx < sizeof(PTP_GLOBAL_All_Registers)/sizeof(REG_All_Registers_t)); idx++) {
		err =  Esu::WriteRegisterList(port, &PTP_GLOBAL_All_Registers[idx]) ;
	}
	return (err) ;
}

int EsuPtpGlobal::Action(void)
{
	uint32_t port = 0x1F ;			// PTP Global: AVB Block == 0x1F
	ioc_data.error = -1 ;

	if (err == 0 && SetDevice(if_name) == true) {

		if (operation == 10) {		// test TimeArray operation
			err = TestTimeArrayRunning(reg_off) ;
		}
		else if (operation == 9) {		// show status operation
			err = ShowPtpGlobalStatus() ;
		}
		else if (operation == 8) {		// stop TimeArray operation
			err = StopTimeArray(reg_off) ;
		}
		else if (operation == 7) {		// start TimeArray operation
			err = StartTimeArray(reg_off, reg_val) ;
		}
		else if (operation == 6) {		// configure PTP Global operation
			err = ConfigurePtpGlobal() ;
		}
		else if (operation == 5) {		// capture TimeArray operation
			err = CaptureTimeArray(reg_off) ;
			if (err == 0) {
				cout << "Successfully captured TimeArray " << reg_off << endl ;
			}
		}
		else if (operation == 4) {		// set bit fields
			err = WriteAllRegisterList(port) ;
		}
		else if (operation == 3) {		// list all registers
			err = DoListPtpGlobal() ;
		}
		else if (operation == 13) {		// write PTP config operation
			err = WritePtpConfig(reg_off, reg_val) ;
			if (err == 0) {
				cout << "PTP Config[0x" << hex << reg_off << "] = 0x" << hex << reg_val << endl ;
			}
		}
		else if (operation == 12) {		// read PTP config operation
			uint32_t configData = 0 ;
			err = ReadPtpConfig(reg_off, &configData) ;
			if (err == 0) {
				cout << "PTP Config[0x" << hex << reg_off << "] = 0x" << hex << configData << endl ;
			}
		}
		else if (operation == 2) {		// write operation
			err = WriteIO(port, reg_off, reg_val) ;
			if (err == 0) {
				cout << "PTP_GLOBAL offset:0x" << hex << setw(8) << setfill('0')
					 << reg_off << " value:0x" << reg_val << endl  ;
			}
		}
		else {	// read operation

			if (operation == 1) {
				err = ReadIO(port, reg_off, &reg_val) ;
				if (err == 0) {
					cout << "PTP_GLOBAL offset:0x" << hex << setw(8) << setfill('0')
						 << reg_off << " value:0x" << reg_val << endl  ;
				}
			}
		}
	}
	return err ;
}

/* *********************************************************************************//**
 * @brief       Test if TimeArray is actually running
 * @details     int EsuPtpGlobal::TestTimeArrayRunning(uint32_t timeArray)
 * @details     Captures time twice with a delay to check if the clock is incrementing
 * @param [in]  timeArray: Time Array to test (0 or 1)
 * @return      error code: 0 if running, non-zero if not running or error
 * *************************************************************************************/
int EsuPtpGlobal::TestTimeArrayRunning(uint32_t timeArray)
{
	uint32_t port = 0x1F ;
	uint32_t todControl = 0 ;
	uint32_t time1_sec_lo, time1_sec_mid, time1_nano_lo, time1_nano_hi ;
	uint32_t time2_sec_lo, time2_sec_mid, time2_nano_lo, time2_nano_hi ;
	uint64_t timestamp1, timestamp2 ;

	if (timeArray > 1) {
		if (verbose) {
			cout << "Error: Invalid TimeArray " << timeArray << ". Valid values are 0 or 1." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	cout << "Testing if TimeArray " << timeArray << " is running..." << endl ;

	// First capture
	todControl = (1 << 15) | (0x4 << 12) | (timeArray << 9) ;  // ToDBusy=1, ToDOp=0x4
	err = WriteIO(port, 0x12, todControl) ;
	if (err != 0) return err ;

	// Wait for completion
	do {
		err = ReadIO(port, 0x12, &todControl) ;
		if (err != 0) return err ;
	} while (todControl & 0x8000) ;

	// Read first timestamp
	err = ReadIO(port, 0x13, &time1_nano_lo) ;
	if (err == 0) err = ReadIO(port, 0x14, &time1_nano_hi) ;
	if (err == 0) err = ReadIO(port, 0x15, &time1_sec_lo) ;
	if (err == 0) err = ReadIO(port, 0x16, &time1_sec_mid) ;
	if (err != 0) return err ;

	timestamp1 = ((uint64_t)time1_sec_mid << 16) | time1_sec_lo ;
	timestamp1 = (timestamp1 * 1000000000ULL) + (((uint64_t)time1_nano_hi << 16) | time1_nano_lo) ;

	// Wait 1 second
	sleep(1) ;

	// Second capture
	todControl = (1 << 15) | (0x4 << 12) | (timeArray << 9) ;  // ToDBusy=1, ToDOp=0x4
	err = WriteIO(port, 0x12, todControl) ;
	if (err != 0) return err ;

	// Wait for completion
	do {
		err = ReadIO(port, 0x12, &todControl) ;
		if (err != 0) return err ;
	} while (todControl & 0x8000) ;

	// Read second timestamp
	err = ReadIO(port, 0x13, &time2_nano_lo) ;
	if (err == 0) err = ReadIO(port, 0x14, &time2_nano_hi) ;
	if (err == 0) err = ReadIO(port, 0x15, &time2_sec_lo) ;
	if (err == 0) err = ReadIO(port, 0x16, &time2_sec_mid) ;
	if (err != 0) return err ;

	timestamp2 = ((uint64_t)time2_sec_mid << 16) | time2_sec_lo ;
	timestamp2 = (timestamp2 * 1000000000ULL) + (((uint64_t)time2_nano_hi << 16) | time2_nano_lo) ;

	// Compare timestamps
	if (timestamp2 > timestamp1) {
		uint64_t diff_ns = timestamp2 - timestamp1 ;
		cout << "✓ TimeArray " << timeArray << " IS RUNNING!" << endl ;
		cout << "  Time advanced by " << (diff_ns / 1000000) << " milliseconds" << endl ;
		return 0 ;
	} else {
		cout << "✗ TimeArray " << timeArray << " is NOT running (time did not advance)" << endl ;
		cout << "  First timestamp:  " << timestamp1 << " ns" << endl ;
		cout << "  Second timestamp: " << timestamp2 << " ns" << endl ;
		return 1 ;
	}
}


/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuPtpGlobal::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuPtpGlobal::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (	strcmp(argv[1], "PTP_GLOBAL") == 0 || strcmp(argv[1], "ptp_global") == 0 ) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
