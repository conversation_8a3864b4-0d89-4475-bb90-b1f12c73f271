/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef PTP_GLOBAL_IO_H
#define PTP_GLOBAL_IO_H

#include "Esu.h"
#include "../register/RegisterIO.h"

/* Type definitions included from file: register/RegisterIO.h
 */
typedef	REG_Field_t		PTP_GLOBAL_Field_t ;
typedef	REG_Register_t		PTP_GLOBAL_Register_t ;
typedef	REG_All_Registers_t	PTP_GLOBAL_All_Registers_t ;

class EsuPtpGlobal : public Esu
{
	public:
				EsuPtpGlobal(int argc, char* argv[]) ;
		CliArgs_t *	SetArgumentList(uint32_t *arg_list_size) ;
		void		SetIfName(char *ifn) { if_name = ifn ; }
		int 		EvaluateArgumentList(int argc, char *argv[]) ;
		int		postAction(void) ;
		int		Action(void) ;
		int 		DoListPtpGlobal() ;

		// 简化的核心 ToD 操作函数
		/* settod */
		int 		SetToDTime(uint32_t timeArray, uint32_t seconds, uint32_t nanoseconds, uint32_t domain) ;
		/* gettod */
		int 		GetToDTime(uint32_t timeArray, uint32_t *seconds, uint32_t *nanoseconds) ;
		/* testtod */
		int 		TestToDRunning(uint32_t timeArray) ;

		// 基础工具函数
		int 		WaitToDReady() ;
		int 		WaitToDComplete() ;

		// 保留的配置函数（简化版）
		int 		ReadPtpConfig(uint32_t pointer, uint32_t *data) ;
		int 		WritePtpConfig(uint32_t pointer, uint32_t data) ;


	static	bool		isResponsible(int argc, char* argv[]) ;

		int		ReadIO(uint32_t dev, uint32_t reg, uint32_t *data) ;
	protected:
		int 		ExecuteCmd(uint32_t port, uint32_t dev, uint32_t reg) ;
		int		WriteIO(uint32_t dev, uint32_t reg, uint32_t data) ;
		int 		WriteAllRegisterList(uint32_t port) ;

		char	*	if_name ;

		/* 0:normal register access, 1:read-modify-write-or, 2:read-modify-write-and */
		int		ioc_flag ;

	private:
		uint32_t	operation ;
		uint32_t	reg_off ;
		uint32_t	reg_val ;

} ;

#endif // PTP_GLOBAL_IO_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
